# ---------- 1. 选择最小 base ----------
    FROM nvidia/cuda:12.4.1-base-ubuntu22.04

    ARG PYTORCH_VERSION=2.3.0
    ARG CUDA_SHORT=124  # 与 wheel 名匹配
    ARG PYTHON_VERSION=3.11
    
    # ---------- 2. 安装最简 Python ----------
    RUN apt-get update -qq && \
        DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends \
            python${PYTHON_VERSION} python3-pip python3-setuptools && \
        ln -sf /usr/bin/python${PYTHON_VERSION} /usr/bin/python && \
        # 清理 apt
        apt-get clean && rm -rf /var/lib/apt/lists/*
    
    # ---------- 3. 安装 PyTorch（仅运行时 wheel） ----------
    # 官方 wheel 名：torch-${PYTORCH_VERSION}+cu${CUDA_SHORT}
    # 使用 --no-deps 避免连带安装额外库；CUDA 运行时已在 base 镜像中
    RUN pip3 install --no-cache-dir --no-deps \
        https://download.pytorch.org/whl/cu${CUDA_SHORT}/torch-${PYTORCH_VERSION}%2Bcu${CUDA_SHORT}-cp${PYTHON_VERSION//.}-cp${PYTHON_VERSION//.}-linux_x86_64.whl
    
    # ---------- 4. 再瘦身 ----------
    RUN rm -rf /opt/conda /usr/local/cuda-*/libnvvp /usr/local/cuda-*/nsight* \
               /usr/local/cuda-*/samples /usr/local/cuda-*/nvvm* \
               /usr/lib/python*/ensurepip* /usr/local/lib/python*/dist-packages/pip*
    
    ENV PYTHONUNBUFFERED=1
    WORKDIR /workspace
    